import { useCallback, useRef } from 'react';

interface AsyncOperationOptions {
  onStart?: () => void;
  onSuccess?: (result: any) => void;
  onError?: (error: Error) => void;
  onFinally?: () => void;
}

interface PendingOperation {
  id: string;
  controller: AbortController;
  timestamp: number;
}

export function useAsyncOperation() {
  const pendingOperationsRef = useRef<Map<string, PendingOperation>>(new Map());
  const latestOperationIdRef = useRef<string | null>(null);

  const execute = useCallback(async <T>(
    operation: (signal: AbortSignal) => Promise<T>,
    options: AsyncOperationOptions = {}
  ): Promise<T | null> => {
    // Generate unique ID for this operation
    const operationId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Create new abort controller for this operation
    const controller = new AbortController();
    const signal = controller.signal;

    // Store this operation as pending
    const pendingOp: PendingOperation = {
      id: operationId,
      controller,
      timestamp: Date.now()
    };

    pendingOperationsRef.current.set(operationId, pendingOp);
    latestOperationIdRef.current = operationId;

    // Clean up old completed operations (older than 30 seconds)
    const now = Date.now();
    for (const [id, op] of pendingOperationsRef.current.entries()) {
      if (now - op.timestamp > 30000) {
        pendingOperationsRef.current.delete(id);
      }
    }

    try {
      options.onStart?.();

      const result = await operation(signal);

      // Check if operation was cancelled
      if (signal.aborted) {
        return null;
      }

      // Only call onSuccess if this is still the latest operation
      if (latestOperationIdRef.current === operationId) {
        options.onSuccess?.(result);
      }

      return result;
    } catch (error) {
      // Don't handle abort errors as real errors
      if (error instanceof Error && error.name === 'AbortError') {
        return null;
      }

      // Only call onError if this is still the latest operation
      if (latestOperationIdRef.current === operationId) {
        options.onError?.(error as Error);
      }
      throw error;
    } finally {
      // Clean up this operation
      pendingOperationsRef.current.delete(operationId);

      if (!signal.aborted && latestOperationIdRef.current === operationId) {
        options.onFinally?.();
      }
    }
  }, []);

  const cancel = useCallback(() => {
    // Cancel all pending operations
    for (const [id, op] of pendingOperationsRef.current.entries()) {
      op.controller.abort();
    }
    pendingOperationsRef.current.clear();
    latestOperationIdRef.current = null;
  }, []);

  const cancelLatest = useCallback(() => {
    // Cancel only the latest operation
    if (latestOperationIdRef.current) {
      const latestOp = pendingOperationsRef.current.get(latestOperationIdRef.current);
      if (latestOp) {
        latestOp.controller.abort();
        pendingOperationsRef.current.delete(latestOperationIdRef.current);
      }
      latestOperationIdRef.current = null;
    }
  }, []);

  return { execute, cancel, cancelLatest };
}
